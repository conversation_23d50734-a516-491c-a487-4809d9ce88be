# AMC 12A 2020 Problem 4 - Proof Tree

## Problem Statement
Count the 4-digit integers whose digits are all even and that are divisible by 5.

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that exactly 100 four-digit integers have all even digits and are divisible by 5
**Strategy**: Combinatorial counting with divisibility constraints

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**: 
1. Analyze divisibility by 5 constraint (units digit must be 0 or 5)
2. Apply even digit constraint to eliminate 5 as units digit
3. Count valid choices for each digit position
4. Apply multiplication principle

**Strategy**: Constraint-based combinatorial counting

### SUBGOAL_001 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Prove units digit must be 0
**Strategy**: Show that divisibility by 5 requires units digit ∈ {0,5}, but even constraint eliminates 5

### SUBGOAL_002 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Count valid choices for thousands digit
**Strategy**: Show d₁ ∈ {2,4,6,8} (non-zero even digits) gives 4 choices

### SUBGOAL_003 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Count valid choices for hundreds digit
**Strategy**: Show d₂ ∈ {0,2,4,6,8} (all even digits) gives 5 choices

### SUBGOAL_004 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Count valid choices for tens digit
**Strategy**: Show d₃ ∈ {0,2,4,6,8} (all even digits) gives 5 choices

### SUBGOAL_005 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Apply multiplication principle
**Strategy**: Show total count = 4 × 5 × 5 × 1 = 100

## Current Status
- Phase: 1 (Initial proof tree generation)
- Active exploration targets: SUBGOAL_001 through SUBGOAL_005
- Next phase: Generate Lean 4 code framework
